from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session
from flask_socketio import So<PERSON><PERSON>, emit, join_room, leave_room
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
import sqlite3
import uuid
import random
import string
from datetime import datetime
import os
from dotenv import load_dotenv
import cloudinary
import cloudinary.uploader
import cloudinary.api
from PIL import Image
import io
import base64
import hashlib
import hmac
import time

# Load environment variables
load_dotenv()

app = Flask(__name__)
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'kawaii-chat-secret-key-2024')
app.config['MAX_CONTENT_LENGTH'] = 50 * 1024 * 1024  # 50MB max file size
socketio = SocketIO(app, cors_allowed_origins="*")

# Configure Cloudinary
cloudinary.config(
    cloud_name=os.getenv('CLOUDINARY_CLOUD_NAME'),
    api_key=os.getenv('CLOUDINARY_API_KEY'),
    api_secret=os.getenv('CLOUDINARY_API_SECRET')
)

# Database setup
DATABASE_URL = os.getenv('DATABASE_URL')
if DATABASE_URL and DATABASE_URL.startswith('postgresql://'):
    # Use PostgreSQL
    import psycopg2
    from urllib.parse import urlparse
    USE_POSTGRES = True
    DATABASE = DATABASE_URL
else:
    # Fallback to SQLite
    USE_POSTGRES = False
    DATABASE = 'kawaii_chat.db'

def get_db_connection():
    """Get database connection"""
    if USE_POSTGRES:
        return psycopg2.connect(DATABASE)
    else:
        return sqlite3.connect(DATABASE)

def init_db():
    """Initialize the database with required tables"""
    conn = get_db_connection()
    cursor = conn.cursor()

    if USE_POSTGRES:
        # PostgreSQL syntax
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id SERIAL PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                contact_code VARCHAR(8) UNIQUE NOT NULL,
                avatar_color VARCHAR(7) DEFAULT '#ff6b9d',
                profile_picture_url TEXT,
                profile_picture_public_id TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        cursor.execute('''
            CREATE TABLE IF NOT EXISTS contacts (
                id SERIAL PRIMARY KEY,
                user_id INTEGER NOT NULL,
                contact_user_id INTEGER NOT NULL,
                contact_name VARCHAR(100),
                added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (contact_user_id) REFERENCES users (id),
                UNIQUE(user_id, contact_user_id)
            )
        ''')

        cursor.execute('''
            CREATE TABLE IF NOT EXISTS messages (
                id SERIAL PRIMARY KEY,
                sender_id INTEGER NOT NULL,
                receiver_id INTEGER NOT NULL,
                message TEXT,
                message_type VARCHAR(20) DEFAULT 'text',
                media_url TEXT,
                media_type VARCHAR(20),
                media_public_id TEXT,
                thumbnail_url TEXT,
                file_size BIGINT,
                sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                read_at TIMESTAMP,
                FOREIGN KEY (sender_id) REFERENCES users (id),
                FOREIGN KEY (receiver_id) REFERENCES users (id)
            )
        ''')

        cursor.execute('''
            CREATE TABLE IF NOT EXISTS message_reactions (
                id SERIAL PRIMARY KEY,
                message_id INTEGER NOT NULL,
                user_id INTEGER NOT NULL,
                emoji VARCHAR(10) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (message_id) REFERENCES messages (id) ON DELETE CASCADE,
                FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
                UNIQUE(message_id, user_id)
            )
        ''')

        cursor.execute('''
            CREATE TABLE IF NOT EXISTS calls (
                id SERIAL PRIMARY KEY,
                caller_id INTEGER NOT NULL,
                receiver_id INTEGER NOT NULL,
                call_type VARCHAR(10) NOT NULL CHECK (call_type IN ('audio', 'video')),
                call_status VARCHAR(20) NOT NULL DEFAULT 'initiated' CHECK (call_status IN ('initiated', 'ringing', 'accepted', 'rejected', 'ended', 'missed')),
                started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                answered_at TIMESTAMP,
                ended_at TIMESTAMP,
                duration INTEGER DEFAULT 0,
                FOREIGN KEY (caller_id) REFERENCES users (id),
                FOREIGN KEY (receiver_id) REFERENCES users (id)
            )
        ''')
    else:
        # SQLite syntax (original)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                contact_code TEXT UNIQUE NOT NULL,
                avatar_color TEXT DEFAULT '#ff6b9d',
                profile_picture_url TEXT,
                profile_picture_public_id TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        cursor.execute('''
            CREATE TABLE IF NOT EXISTS contacts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                contact_user_id INTEGER NOT NULL,
                contact_name TEXT,
                added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (contact_user_id) REFERENCES users (id),
                UNIQUE(user_id, contact_user_id)
            )
        ''')

        cursor.execute('''
            CREATE TABLE IF NOT EXISTS messages (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sender_id INTEGER NOT NULL,
                receiver_id INTEGER NOT NULL,
                message TEXT,
                message_type TEXT DEFAULT 'text',
                media_url TEXT,
                media_type TEXT,
                media_public_id TEXT,
                thumbnail_url TEXT,
                file_size INTEGER,
                reply_to_message_id INTEGER,
                sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                read_at TIMESTAMP,
                FOREIGN KEY (sender_id) REFERENCES users (id),
                FOREIGN KEY (receiver_id) REFERENCES users (id),
                FOREIGN KEY (reply_to_message_id) REFERENCES messages (id)
            )
        ''')

        cursor.execute('''
            CREATE TABLE IF NOT EXISTS message_reactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                message_id INTEGER NOT NULL,
                user_id INTEGER NOT NULL,
                emoji TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (message_id) REFERENCES messages (id) ON DELETE CASCADE,
                FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
                UNIQUE(message_id, user_id)
            )
        ''')

        cursor.execute('''
            CREATE TABLE IF NOT EXISTS calls (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                caller_id INTEGER NOT NULL,
                receiver_id INTEGER NOT NULL,
                call_type TEXT NOT NULL CHECK (call_type IN ('audio', 'video')),
                call_status TEXT NOT NULL DEFAULT 'initiated' CHECK (call_status IN ('initiated', 'ringing', 'accepted', 'rejected', 'ended', 'missed')),
                started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                answered_at TIMESTAMP,
                ended_at TIMESTAMP,
                duration INTEGER DEFAULT 0,
                FOREIGN KEY (caller_id) REFERENCES users (id),
                FOREIGN KEY (receiver_id) REFERENCES users (id)
            )
        ''')

        # Add reply_to_message_id column if it doesn't exist (migration)
        try:
            cursor.execute('ALTER TABLE messages ADD COLUMN reply_to_message_id INTEGER REFERENCES messages(id)')
            print("✅ Added reply_to_message_id column to messages table")
        except Exception as e:
            if "duplicate column name" in str(e).lower() or "already exists" in str(e).lower():
                print("ℹ️ reply_to_message_id column already exists")
            else:
                print(f"⚠️ Error adding reply_to_message_id column: {e}")
    
    conn.commit()
    conn.close()

def migrate_db():
    """Migrate database to add new columns if they don't exist"""
    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        # Check if profile_picture_url column exists
        if USE_POSTGRES:
            cursor.execute("""
                SELECT column_name FROM information_schema.columns
                WHERE table_name='users' AND column_name='profile_picture_url'
            """)
            result = cursor.fetchone()
            if not result:
                # Add profile picture columns to PostgreSQL
                cursor.execute("ALTER TABLE users ADD COLUMN profile_picture_url TEXT")
                cursor.execute("ALTER TABLE users ADD COLUMN profile_picture_public_id TEXT")
                print("Added profile picture columns to PostgreSQL users table! 📷")
        else:
            cursor.execute("PRAGMA table_info(users)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'profile_picture_url' not in columns:
                # Add profile picture columns to SQLite
                cursor.execute("ALTER TABLE users ADD COLUMN profile_picture_url TEXT")
                cursor.execute("ALTER TABLE users ADD COLUMN profile_picture_public_id TEXT")
                print("Added profile picture columns to SQLite users table! 📷")

        # Check if read_at column exists in messages table
        if USE_POSTGRES:
            cursor.execute("""
                SELECT column_name FROM information_schema.columns
                WHERE table_name='messages' AND column_name='read_at'
            """)
            result = cursor.fetchone()
            if not result:
                cursor.execute("ALTER TABLE messages ADD COLUMN read_at TIMESTAMP")
                print("Added read_at column to PostgreSQL messages table! 📖")
        else:
            cursor.execute("PRAGMA table_info(messages)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'read_at' not in columns:
                cursor.execute("ALTER TABLE messages ADD COLUMN read_at TIMESTAMP")
                print("Added read_at column to SQLite messages table! 📖")

        # Check and add media columns for image support
        if USE_POSTGRES:
            # Check for media columns
            cursor.execute("""
                SELECT column_name FROM information_schema.columns
                WHERE table_name='messages' AND column_name IN ('media_url', 'media_type', 'media_public_id', 'thumbnail_url', 'file_size')
            """)
            existing_media_columns = [row[0] for row in cursor.fetchall()]

            media_columns = [
                ('media_url', 'TEXT'),
                ('media_type', 'VARCHAR(20)'),
                ('media_public_id', 'TEXT'),
                ('thumbnail_url', 'TEXT'),
                ('file_size', 'BIGINT')
            ]

            for col_name, col_type in media_columns:
                if col_name not in existing_media_columns:
                    cursor.execute(f"ALTER TABLE messages ADD COLUMN {col_name} {col_type}")
                    print(f"Added {col_name} column to PostgreSQL messages table! 📷")
        else:
            cursor.execute("PRAGMA table_info(messages)")
            columns = [column[1] for column in cursor.fetchall()]

            media_columns = [
                ('media_url', 'TEXT'),
                ('media_type', 'TEXT'),
                ('media_public_id', 'TEXT'),
                ('thumbnail_url', 'TEXT'),
                ('file_size', 'INTEGER')
            ]

            for col_name, col_type in media_columns:
                if col_name not in columns:
                    cursor.execute(f"ALTER TABLE messages ADD COLUMN {col_name} {col_type}")
                    print(f"Added {col_name} column to SQLite messages table! 📷")

        # Add call-specific columns
        if USE_POSTGRES:
            cursor.execute("""
                SELECT column_name FROM information_schema.columns
                WHERE table_name = 'messages' AND column_name IN ('call_status', 'call_duration')
            """)
            existing_call_columns = [row[0] for row in cursor.fetchall()]

            call_columns = [
                ('call_status', 'VARCHAR(20)'),
                ('call_duration', 'INTEGER')
            ]

            for col_name, col_type in call_columns:
                if col_name not in existing_call_columns:
                    cursor.execute(f"ALTER TABLE messages ADD COLUMN {col_name} {col_type}")
                    print(f"✅ Added {col_name} column to PostgreSQL messages table! 📞")
        else:
            cursor.execute("PRAGMA table_info(messages)")
            columns = [column[1] for column in cursor.fetchall()]

            call_columns = [
                ('call_status', 'TEXT'),
                ('call_duration', 'INTEGER')
            ]

            for col_name, col_type in call_columns:
                if col_name not in columns:
                    cursor.execute(f"ALTER TABLE messages ADD COLUMN {col_name} {col_type}")
                    print(f"✅ Added {col_name} column to SQLite messages table! 📞")

        # Check if message_reactions table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='message_reactions'")
        if not cursor.fetchone():
            print("🔧 Creating message_reactions table...")
            cursor.execute('''
                CREATE TABLE message_reactions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    message_id INTEGER NOT NULL,
                    user_id INTEGER NOT NULL,
                    emoji TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (message_id) REFERENCES messages (id) ON DELETE CASCADE,
                    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
                    UNIQUE(message_id, user_id)
                )
            ''')
            print("✅ Created message_reactions table! 👍")
        else:
            print("✅ message_reactions table already exists! 👍")

        # Check if calls table exists
        if USE_POSTGRES:
            cursor.execute("""
                SELECT table_name FROM information_schema.tables
                WHERE table_name='calls'
            """)
            if not cursor.fetchone():
                print("🔧 Creating calls table for PostgreSQL...")
                cursor.execute('''
                    CREATE TABLE calls (
                        id SERIAL PRIMARY KEY,
                        caller_id INTEGER NOT NULL,
                        receiver_id INTEGER NOT NULL,
                        call_type VARCHAR(10) NOT NULL CHECK (call_type IN ('audio', 'video')),
                        call_status VARCHAR(20) NOT NULL DEFAULT 'initiated' CHECK (call_status IN ('initiated', 'ringing', 'accepted', 'rejected', 'ended', 'missed')),
                        started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        answered_at TIMESTAMP,
                        ended_at TIMESTAMP,
                        duration INTEGER DEFAULT 0,
                        FOREIGN KEY (caller_id) REFERENCES users (id),
                        FOREIGN KEY (receiver_id) REFERENCES users (id)
                    )
                ''')
                print("✅ Created calls table for PostgreSQL! 📞")
            else:
                print("✅ calls table already exists for PostgreSQL! 📞")
        else:
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='calls'")
            if not cursor.fetchone():
                print("🔧 Creating calls table for SQLite...")
                cursor.execute('''
                    CREATE TABLE calls (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        caller_id INTEGER NOT NULL,
                        receiver_id INTEGER NOT NULL,
                        call_type TEXT NOT NULL CHECK (call_type IN ('audio', 'video')),
                        call_status TEXT NOT NULL DEFAULT 'initiated' CHECK (call_status IN ('initiated', 'ringing', 'accepted', 'rejected', 'ended', 'missed')),
                        started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        answered_at TIMESTAMP,
                        ended_at TIMESTAMP,
                        duration INTEGER DEFAULT 0,
                        FOREIGN KEY (caller_id) REFERENCES users (id),
                        FOREIGN KEY (receiver_id) REFERENCES users (id)
                    )
                ''')
                print("✅ Created calls table for SQLite! 📞")
            else:
                print("✅ calls table already exists for SQLite! 📞")

        conn.commit()

    except Exception as e:
        print(f"Migration error (this might be normal if columns already exist): {e}")
    finally:
        conn.close()

def generate_contact_code():
    """Generate a unique 8-character contact code"""
    while True:
        code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))
        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()
        cursor.execute('SELECT id FROM users WHERE contact_code = ?', (code,))
        if not cursor.fetchone():
            conn.close()
            return code
        conn.close()

def get_user_by_username(username):
    """Get user by username"""
    conn = get_db_connection()
    cursor = conn.cursor()
    if USE_POSTGRES:
        cursor.execute('SELECT * FROM users WHERE username = %s', (username,))
    else:
        cursor.execute('SELECT * FROM users WHERE username = ?', (username,))
    user = cursor.fetchone()
    conn.close()
    return user

def get_user_by_id(user_id):
    """Get user by ID"""
    conn = get_db_connection()
    cursor = conn.cursor()
    if USE_POSTGRES:
        cursor.execute('SELECT * FROM users WHERE id = %s', (user_id,))
    else:
        cursor.execute('SELECT * FROM users WHERE id = ?', (user_id,))
    user = cursor.fetchone()
    conn.close()
    return user

def get_user_by_contact_code(contact_code):
    """Get user by contact code"""
    conn = get_db_connection()
    cursor = conn.cursor()
    if USE_POSTGRES:
        cursor.execute('SELECT * FROM users WHERE contact_code = %s', (contact_code,))
    else:
        cursor.execute('SELECT * FROM users WHERE contact_code = ?', (contact_code,))
    user = cursor.fetchone()
    conn.close()
    return user

def upload_to_cloudinary(file, resource_type="auto"):
    """Upload file to Cloudinary"""
    try:
        # Generate unique filename
        filename = f"kawaii_chat_{uuid.uuid4().hex}"

        # Upload to Cloudinary
        result = cloudinary.uploader.upload(
            file,
            public_id=filename,
            resource_type=resource_type,
            folder="kawaii_chat",
            transformation=[
                {"quality": "auto:good"},
                {"fetch_format": "auto"}
            ]
        )

        return {
            'success': True,
            'url': result['secure_url'],
            'public_id': result['public_id'],
            'resource_type': result['resource_type'],
            'format': result['format'],
            'bytes': result['bytes']
        }
    except Exception as e:
        print(f"Cloudinary upload error: {e}")
        return {'success': False, 'error': str(e)}

def create_thumbnail(image_url):
    """Create thumbnail for images"""
    try:
        # Use Cloudinary's transformation to create thumbnail
        thumbnail_url = cloudinary.CloudinaryImage(image_url).build_url(
            width=200,
            height=200,
            crop="fill",
            quality="auto:good",
            fetch_format="auto"
        )
        return thumbnail_url
    except Exception as e:
        print(f"Thumbnail creation error: {e}")
        return image_url

def get_file_type(filename):
    """Determine file type from filename"""
    ext = filename.lower().split('.')[-1] if '.' in filename else ''

    image_exts = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp']
    video_exts = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv']
    audio_exts = ['mp3', 'wav', 'ogg', 'aac', 'flac', 'm4a']

    if ext in image_exts:
        return 'image'
    elif ext in video_exts:
        return 'video'
    elif ext in audio_exts:
        return 'audio'
    else:
        return 'document'

# Routes
@app.route('/')
def index():
    """Home page - redirect to login if not authenticated"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return redirect(url_for('chat'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    """Login page"""
    if request.method == 'POST':
        username = request.form['username'].strip()
        password = request.form['password']
        
        if not username or not password:
            flash('Please fill in all fields! 💕', 'error')
            return render_template('login.html')
        
        user = get_user_by_username(username)
        if user and check_password_hash(user[2], password):
            session['user_id'] = user[0]
            session['username'] = user[1]
            session['contact_code'] = user[3]
            session['profile_picture_url'] = user[6] if len(user) > 6 else None
            flash(f'Welcome back, {username}! 🌸', 'success')
            return redirect(url_for('chat'))
        else:
            flash('Invalid username or password! 😢', 'error')
    
    return render_template('login.html')

@app.route('/signup', methods=['GET', 'POST'])
def signup():
    """Signup page"""
    if request.method == 'POST':
        username = request.form['username'].strip()
        password = request.form['password']
        confirm_password = request.form['confirm_password']
        
        if not username or not password or not confirm_password:
            flash('Please fill in all fields! 💕', 'error')
            return render_template('signup.html')
        
        if password != confirm_password:
            flash('Passwords do not match! 😅', 'error')
            return render_template('signup.html')
        
        if len(password) < 6:
            flash('Password must be at least 6 characters! 🔒', 'error')
            return render_template('signup.html')
        
        # Check if username already exists
        if get_user_by_username(username):
            flash('Username already exists! Try another one! 🦄', 'error')
            return render_template('signup.html')
        
        # Create new user
        password_hash = generate_password_hash(password)
        contact_code = generate_contact_code()
        avatar_colors = ['#ff6b9d', '#c44569', '#f8b500', '#6c5ce7', '#a29bfe', '#fd79a8']
        avatar_color = random.choice(avatar_colors)
        
        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO users (username, password_hash, contact_code, avatar_color)
            VALUES (?, ?, ?, ?)
        ''', (username, password_hash, contact_code, avatar_color))
        user_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        session['user_id'] = user_id
        session['username'] = username
        session['contact_code'] = contact_code
        flash(f'Welcome to Kawaii Chat, {username}! Your contact code is: {contact_code} 🎀', 'success')
        return redirect(url_for('profile'))
    
    return render_template('signup.html')

@app.route('/logout')
def logout():
    """Logout user"""
    session.clear()
    flash('Goodbye! Come back soon! 👋', 'info')
    return redirect(url_for('login'))

@app.route('/profile')
def profile():
    """User profile page"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    user = get_user_by_id(session['user_id'])
    if not user:
        return redirect(url_for('logout'))

    return render_template('profile.html', user=user)

@app.route('/chat')
def chat():
    """Main chat page"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    # Get user's contacts with last_seen info
    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()
    if USE_POSTGRES:
        cursor.execute('''
            SELECT u.id, u.username, u.avatar_color, c.contact_name, u.contact_code, u.profile_picture_url, u.last_seen
            FROM contacts c
            JOIN users u ON c.contact_user_id = u.id
            WHERE c.user_id = %s
            ORDER BY c.added_at DESC
        ''', (session['user_id'],))
    else:
        cursor.execute('''
            SELECT u.id, u.username, u.avatar_color, c.contact_name, u.contact_code, u.profile_picture_url, u.last_seen
            FROM contacts c
            JOIN users u ON c.contact_user_id = u.id
            WHERE c.user_id = ?
            ORDER BY c.added_at DESC
        ''', (session['user_id'],))
    contacts = cursor.fetchall()
    conn.close()
    
    return render_template('chat.html', contacts=contacts)

@app.route('/add_contact', methods=['POST'])
def add_contact():
    """Add a contact using contact code"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': 'Not authenticated'})

    try:
        contact_code = request.json.get('contact_code', '').strip().upper()
        contact_name = request.json.get('contact_name', '').strip()

        print(f"DEBUG: Add contact request - User ID: {session['user_id']}, Code: {contact_code}, Name: {contact_name}")

        if not contact_code:
            return jsonify({'success': False, 'message': 'Please enter a contact code! 💕'})

        # Check if it's user's own code
        if contact_code == session['contact_code']:
            return jsonify({'success': False, 'message': "You can't add yourself! 😅"})

        # Find user by contact code
        contact_user = get_user_by_contact_code(contact_code)
        print(f"DEBUG: Contact lookup result: {contact_user}")

        if not contact_user:
            return jsonify({'success': False, 'message': 'Contact code not found! 😢'})

        # Check if already added
        conn = get_db_connection()
        cursor = conn.cursor()

        if USE_POSTGRES:
            cursor.execute('SELECT id FROM contacts WHERE user_id = %s AND contact_user_id = %s',
                           (session['user_id'], contact_user[0]))
        else:
            cursor.execute('SELECT id FROM contacts WHERE user_id = ? AND contact_user_id = ?',
                           (session['user_id'], contact_user[0]))

        if cursor.fetchone():
            conn.close()
            return jsonify({'success': False, 'message': 'Contact already added! 🦄'})

        # Add contact
        display_name = contact_name if contact_name else contact_user[1]
        print(f"DEBUG: Adding contact - User ID: {session['user_id']}, Contact User ID: {contact_user[0]}, Display Name: {display_name}")

        if USE_POSTGRES:
            cursor.execute('''
                INSERT INTO contacts (user_id, contact_user_id, contact_name)
                VALUES (%s, %s, %s)
            ''', (session['user_id'], contact_user[0], display_name))
        else:
            cursor.execute('''
                INSERT INTO contacts (user_id, contact_user_id, contact_name)
                VALUES (?, ?, ?)
            ''', (session['user_id'], contact_user[0], display_name))

        conn.commit()
        print(f"DEBUG: Contact added successfully to database")
        conn.close()

        return jsonify({
            'success': True,
            'message': f'Added {display_name} to your contacts! 🌸',
            'contact': {
                'id': contact_user[0],
                'username': contact_user[1],
                'display_name': display_name,
                'avatar_color': contact_user[4],
                'profile_picture_url': contact_user[6] if len(contact_user) > 6 else None
            }
        })

    except Exception as e:
        print(f"DEBUG: Error in add_contact: {e}")
        return jsonify({'success': False, 'message': f'Error adding contact: {str(e)}'})

@app.route('/api/cloudinary-signature', methods=['POST'])
def generate_cloudinary_signature():
    """Generate signature for Cloudinary upload"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401

    try:
        data = request.get_json()
        timestamp = data.get('timestamp', int(time.time()))
        resource_type = data.get('resource_type', 'auto')

        # Cloudinary credentials
        api_key = '141354373965837'
        api_secret = 'wetCyuAA-PphcvjfbmjwRxrrpGs'

        # Parameters to sign
        params = {
            'timestamp': timestamp,
            'resource_type': resource_type
        }

        # Create signature string
        params_string = '&'.join([f'{k}={v}' for k, v in sorted(params.items())])
        signature_string = params_string + api_secret

        # Generate signature
        signature = hashlib.sha1(signature_string.encode('utf-8')).hexdigest()

        return jsonify({
            'signature': signature,
            'timestamp': timestamp,
            'api_key': api_key
        })

    except Exception as e:
        print(f"❌ Signature generation error: {e}")
        return jsonify({'error': 'Signature generation failed'}), 500

@app.route('/upload_media', methods=['POST'])
def upload_media():
    """Upload media file to Cloudinary"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': 'Not authenticated'}), 401

    if 'file' not in request.files:
        return jsonify({'success': False, 'message': 'No file provided'}), 400

    file = request.files['file']
    receiver_id = request.form.get('receiver_id')
    caption = request.form.get('caption', '')

    if not file or file.filename == '':
        return jsonify({'success': False, 'message': 'No file selected'}), 400

    if not receiver_id:
        return jsonify({'success': False, 'message': 'No receiver specified'}), 400

    # Check file size (50MB limit)
    file.seek(0, 2)  # Seek to end
    file_size = file.tell()
    file.seek(0)  # Reset to beginning

    if file_size > 50 * 1024 * 1024:
        return jsonify({'success': False, 'message': 'File too large! Max 50MB 📁'}), 400

    # Determine file type
    file_type = get_file_type(file.filename)

    # Upload to Cloudinary
    upload_result = upload_to_cloudinary(file, resource_type="auto")

    if not upload_result['success']:
        return jsonify({'success': False, 'message': 'Upload failed 😢'}), 500

    # Create thumbnail for images
    thumbnail_url = None
    if file_type == 'image':
        thumbnail_url = create_thumbnail(upload_result['public_id'])

    # Save message to database
    conn = get_db_connection()
    cursor = conn.cursor()

    if USE_POSTGRES:
        cursor.execute('''
            INSERT INTO messages (sender_id, receiver_id, message, message_type,
                                media_url, media_type, file_size)
            VALUES (%s, %s, %s, %s, %s, %s, %s) RETURNING id
        ''', (session['user_id'], receiver_id, caption, 'file',
              upload_result['url'], file_type, upload_result.get('bytes', file_size)))
        message_id = cursor.fetchone()[0]
    else:
        cursor.execute('''
            INSERT INTO messages (sender_id, receiver_id, message, message_type,
                                media_url, media_type, file_size)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (session['user_id'], receiver_id, caption, 'file',
              upload_result['url'], file_type, upload_result.get('bytes', file_size)))
        message_id = cursor.lastrowid

    conn.commit()
    conn.close()

    # Get sender info
    sender = get_user_by_id(session['user_id'])

    # Prepare message data
    message_data = {
        'id': message_id,
        'sender_id': session['user_id'],
        'sender_username': sender[1],
        'sender_avatar_color': sender[4],
        'receiver_id': int(receiver_id),
        'message': caption,
        'message_type': file_type,
        'media_url': upload_result['url'],
        'thumbnail_url': thumbnail_url,
        'file_size': upload_result['bytes'],
        'sent_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'is_own': True
    }

    # Send to receiver via socket
    receiver_room = f"user_{receiver_id}"
    socketio.emit('new_message', {**message_data, 'is_own': False}, room=receiver_room)

    return jsonify({'success': True, 'message': message_data})

@app.route('/send_message', methods=['POST'])
def send_message_http():
    """HTTP endpoint for sending messages (fallback)"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': 'Not authenticated'}), 401

    try:
        data = request.get_json()
        receiver_id = data.get('receiver_id')
        message = data.get('message', '').strip()
        temp_id = data.get('temp_id')

        # File data
        file_url = data.get('file_url')
        file_name = data.get('file_name')
        file_size = data.get('file_size')
        file_type = data.get('file_type')

        # Check if it's a file message or text message
        is_file_message = bool(file_url)

        if not receiver_id or (not message and not is_file_message):
            return jsonify({'success': False, 'message': 'Missing receiver_id or message/file'}), 400

        # Validate receiver exists and is a contact
        conn = get_db_connection()
        cursor = conn.cursor()

        if USE_POSTGRES:
            cursor.execute('''
                SELECT COUNT(*) FROM contacts c
                JOIN users u ON c.contact_user_id = u.id
                WHERE c.user_id = %s AND u.id = %s
            ''', (session['user_id'], receiver_id))
        else:
            cursor.execute('''
                SELECT COUNT(*) FROM contacts c
                JOIN users u ON c.contact_user_id = u.id
                WHERE c.user_id = ? AND u.id = ?
            ''', (session['user_id'], receiver_id))

        if cursor.fetchone()[0] == 0:
            conn.close()
            return jsonify({'success': False, 'message': 'Invalid receiver'}), 400

        # Insert message into database
        if USE_POSTGRES:
            cursor.execute('''
                INSERT INTO messages (sender_id, receiver_id, message, sent_at)
                VALUES (%s, %s, %s, NOW())
                RETURNING id, sent_at
            ''', (session['user_id'], receiver_id, message))
        else:
            cursor.execute('''
                INSERT INTO messages (sender_id, receiver_id, message, sent_at)
                VALUES (?, ?, ?, datetime('now'))
            ''', (session['user_id'], receiver_id, message))

        if USE_POSTGRES:
            result = cursor.fetchone()
            message_id = result[0]
            sent_at = result[1]
        else:
            message_id = cursor.lastrowid
            cursor.execute('SELECT sent_at FROM messages WHERE id = ?', (message_id,))
            sent_at = cursor.fetchone()[0]

        conn.commit()
        conn.close()

        # Format message data
        message_data = {
            'id': message_id,
            'sender_id': session['user_id'],
            'receiver_id': receiver_id,
            'message': message,
            'sent_at': sent_at.strftime('%Y-%m-%d %H:%M:%S') if hasattr(sent_at, 'strftime') else str(sent_at),
            'sender_name': session.get('username', 'Unknown'),
            'temp_id': temp_id
        }

        # Emit to receiver via Socket.IO if connected
        receiver_room = f"user_{receiver_id}"
        socketio.emit('new_message', {**message_data, 'is_own': False}, room=receiver_room)

        return jsonify({'success': True, 'message': message_data})

    except Exception as e:
        print(f"Error sending message: {e}")
        return jsonify({'success': False, 'message': 'Failed to send message'}), 500

@app.route('/test_realtime')
def test_realtime():
    """Test page for real-time messaging"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('test_realtime.html')

@app.route('/media_test')
def media_test():
    """Test page for media access debugging"""
    return render_template('media_test.html')

@app.route('/upload_profile_picture', methods=['POST'])
def upload_profile_picture():
    """Upload profile picture to Cloudinary"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': 'Not authenticated'}), 401

    if 'file' not in request.files:
        return jsonify({'success': False, 'message': 'No file provided'}), 400

    file = request.files['file']

    if not file or file.filename == '':
        return jsonify({'success': False, 'message': 'No file selected'}), 400

    # Check file size (10MB limit for profile pictures)
    file.seek(0, 2)  # Seek to end
    file_size = file.tell()
    file.seek(0)  # Reset to beginning

    if file_size > 10 * 1024 * 1024:
        return jsonify({'success': False, 'message': 'File too large! Max 10MB for profile pictures 📷'}), 400

    # Check if it's an image
    if not file.content_type.startswith('image/'):
        return jsonify({'success': False, 'message': 'Please upload an image file! 🖼️'}), 400

    try:
        # Get current user to delete old profile picture if exists
        user = get_user_by_id(session['user_id'])
        old_public_id = user[7] if len(user) > 7 else None  # profile_picture_public_id

        # Upload to Cloudinary with transformations for profile picture
        filename = f"profile_{session['user_id']}_{uuid.uuid4().hex}"

        result = cloudinary.uploader.upload(
            file,
            public_id=filename,
            folder="kawaii_chat/profiles",
            transformation=[
                {"width": 200, "height": 200, "crop": "fill", "gravity": "face"},
                {"quality": "auto:good"},
                {"fetch_format": "auto"}
            ]
        )

        # Update user profile in database
        conn = get_db_connection()
        cursor = conn.cursor()

        if USE_POSTGRES:
            cursor.execute('''
                UPDATE users
                SET profile_picture_url = %s, profile_picture_public_id = %s
                WHERE id = %s
            ''', (result['secure_url'], result['public_id'], session['user_id']))
        else:
            cursor.execute('''
                UPDATE users
                SET profile_picture_url = ?, profile_picture_public_id = ?
                WHERE id = ?
            ''', (result['secure_url'], result['public_id'], session['user_id']))

        conn.commit()
        conn.close()

        # Update session with new profile picture URL
        session['profile_picture_url'] = result['secure_url']

        # Delete old profile picture from Cloudinary if exists
        if old_public_id:
            try:
                cloudinary.uploader.destroy(old_public_id)
            except Exception as e:
                print(f"Error deleting old profile picture: {e}")

        return jsonify({
            'success': True,
            'message': 'Profile picture updated! 📷✨',
            'profile_picture_url': result['secure_url']
        })

    except Exception as e:
        print(f"Profile picture upload error: {e}")
        return jsonify({'success': False, 'message': 'Upload failed 😢'}), 500

# Socket.IO Events
@socketio.on('connect')
def handle_connect():
    """Handle client connection"""
    if 'user_id' not in session:
        print("❌ Connection rejected: No user_id in session")
        return False

    user_room = f"user_{session['user_id']}"
    join_room(user_room)
    print(f"✅ User {session['user_id']} connected and joined room: {user_room}")
    print(f"✅ Session ID: {request.sid}")
    emit('connected', {'message': 'Connected to Kawaii Chat! 🌸'})

    # Don't automatically mark pending messages as delivered
    # Only mark as delivered when messages are actually received and displayed
    print(f"📱 User {session['user_id']} connected - no fake delivery confirmations")

    # Update user's last seen to now (they're online)
    update_user_last_seen(session['user_id'])

    # Broadcast user online status to their contacts
    print(f"🔄 About to broadcast ONLINE status for user {session['user_id']}")
    broadcast_user_status(session['user_id'], 'online')

def update_user_last_seen(user_id):
    """Update user's last seen timestamp"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Check if last_seen column exists, if not add it
        if USE_POSTGRES:
            cursor.execute('''
                ALTER TABLE users ADD COLUMN IF NOT EXISTS last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ''')
        else:
            # For SQLite, check if column exists first
            cursor.execute("PRAGMA table_info(users)")
            columns = [column[1] for column in cursor.fetchall()]
            if 'last_seen' not in columns:
                cursor.execute('ALTER TABLE users ADD COLUMN last_seen DATETIME')
                print("✅ Added last_seen column to users table")

        # Update user's last seen timestamp
        if USE_POSTGRES:
            cursor.execute('''
                UPDATE users SET last_seen = CURRENT_TIMESTAMP WHERE id = %s
            ''', (user_id,))
        else:
            cursor.execute('''
                UPDATE users SET last_seen = datetime('now') WHERE id = ?
            ''', (user_id,))

        conn.commit()
        conn.close()
        print(f"📱 Updated last_seen for user {user_id}")

    except Exception as e:
        print(f"❌ Error updating last_seen for user {user_id}: {e}")

def mark_pending_messages_as_delivered(user_id):
    """DISABLED: This function was creating fake delivery confirmations"""
    # This function was automatically marking all unread messages as "delivered"
    # when users connected, which created fake delivery status.
    # Real delivery should only happen when messages are actually received and displayed.
    print(f"📱 User {user_id} connected - fake delivery marking disabled")

def broadcast_user_status(user_id, status):
    """Broadcast user online/offline status to their contacts with last_seen info"""
    try:
        # Get user's contacts and last_seen info
        conn = get_db_connection()
        cursor = conn.cursor()

        # Get user's last_seen timestamp
        if USE_POSTGRES:
            cursor.execute('SELECT last_seen FROM users WHERE id = %s', (user_id,))
        else:
            cursor.execute('SELECT last_seen FROM users WHERE id = ?', (user_id,))

        user_result = cursor.fetchone()
        last_seen = user_result[0] if user_result else None

        # Get user's contacts
        if USE_POSTGRES:
            cursor.execute('''
                SELECT DISTINCT contact_user_id as contact_id
                FROM contacts
                WHERE user_id = %s
                UNION
                SELECT DISTINCT user_id as contact_id
                FROM contacts
                WHERE contact_user_id = %s
            ''', (user_id, user_id))
        else:
            cursor.execute('''
                SELECT DISTINCT contact_user_id as contact_id
                FROM contacts
                WHERE user_id = ?
                UNION
                SELECT DISTINCT user_id as contact_id
                FROM contacts
                WHERE contact_user_id = ?
            ''', (user_id, user_id))

        contacts = cursor.fetchall()
        conn.close()

        # Broadcast status to each contact
        for (contact_id,) in contacts:
            contact_room = f"user_{contact_id}"
            socketio.emit('user_status_changed', {
                'user_id': user_id,
                'status': status,
                'last_seen': last_seen
            }, room=contact_room)

        print(f"📡 Broadcasted {status} status for user {user_id} to {len(contacts)} contacts (last_seen: {last_seen})")

    except Exception as e:
        print(f"❌ Error broadcasting user status: {e}")

@socketio.on('message_received')
def handle_message_received(data):
    """Handle delivery confirmation when receiver gets the message"""
    if 'user_id' not in session:
        return

    message_id = data.get('message_id')
    sender_id = data.get('sender_id')

    if message_id and sender_id:
        # Send delivery confirmation to sender
        sender_room = f"user_{sender_id}"
        socketio.emit('message_delivered', {
            'message_id': message_id,
            'temp_id': None
        }, room=sender_room)
        print(f"📨 Message {message_id} delivered to receiver {session['user_id']}, notified sender {sender_id}")

@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection"""
    if 'user_id' in session:
        user_room = f"user_{session['user_id']}"
        leave_room(user_room)
        print(f"❌ User {session['user_id']} disconnected from room: {user_room}")

        # Update user's last seen to now (when they went offline)
        update_user_last_seen(session['user_id'])

        # Broadcast user offline status to their contacts
        print(f"🔄 About to broadcast OFFLINE status for user {session['user_id']}")
        broadcast_user_status(session['user_id'], 'offline')

@socketio.on('test_echo')
def handle_test_echo(data):
    """Test echo for debugging socket connection"""
    print(f"🧪 Test echo received from user {session.get('user_id', 'unknown')}: {data}")
    emit('test_echo_response', {
        'original': data,
        'server_timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'user_id': session.get('user_id'),
        'message': 'Echo test successful!'
    })

@socketio.on('debug_test_call')
def handle_debug_test_call(data):
    """Debug endpoint to test call notifications"""
    if 'user_id' not in session:
        return

    receiver_id = data.get('receiver_id')
    if not receiver_id:
        return

    print(f"🐛 DEBUG: Testing call notification to user {receiver_id}")

    # Create test call data
    test_call_data = {
        'call_id': f'debug_{int(time.time())}',
        'caller_id': session['user_id'],
        'caller_username': f'Debug Caller (User {session["user_id"]})',
        'caller_avatar_color': '#ff6b9d',
        'caller_profile_picture': None,
        'call_type': 'audio',
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }

    # Send to receiver
    receiver_room = f"user_{receiver_id}"
    print(f"🐛 DEBUG: Sending test call to room {receiver_room}")
    socketio.emit('incoming_call', test_call_data, room=receiver_room)

    # Confirm to sender
    emit('debug_test_sent', {'message': f'Test call sent to user {receiver_id}'})

@socketio.on('send_message')
def handle_send_message(data):
    """Handle sending a message"""
    if 'user_id' not in session:
        return

    receiver_id = data.get('receiver_id')
    message = data.get('message', '').strip()
    temp_id = data.get('temp_id')
    reply_to_message_id = data.get('reply_to_message_id')

    # File data
    file_url = data.get('file_url')
    file_name = data.get('file_name')
    file_size = data.get('file_size')
    file_type = data.get('file_type')

    # Check if it's a file message or text message
    is_file_message = bool(file_url)

    if not receiver_id or (not message and not is_file_message):
        print(f"❌ Missing data: receiver_id={receiver_id}, message='{message}', file_url={file_url}")
        return

    # Convert receiver_id to integer
    try:
        receiver_id = int(receiver_id)
        if is_file_message:
            print(f"📎 Processing file message: sender={session['user_id']}, receiver={receiver_id}, file='{file_name}'")
        else:
            print(f"📤 Processing message: sender={session['user_id']}, receiver={receiver_id}, message='{message}'")
    except (ValueError, TypeError):
        print(f"❌ Invalid receiver_id: {receiver_id}")
        return

    # Save message to database
    conn = get_db_connection()
    cursor = conn.cursor()

    if USE_POSTGRES:
        if is_file_message:
            cursor.execute('''
                INSERT INTO messages (sender_id, receiver_id, message, message_type, media_url, media_type, file_size, reply_to_message_id, sent_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, NOW())
                RETURNING id
            ''', (session['user_id'], receiver_id, message or file_name, 'file', file_url, file_type, file_size, reply_to_message_id))
        else:
            cursor.execute('''
                INSERT INTO messages (sender_id, receiver_id, message, reply_to_message_id, sent_at)
                VALUES (%s, %s, %s, %s, NOW())
                RETURNING id
            ''', (session['user_id'], receiver_id, message, reply_to_message_id))
        message_id = cursor.fetchone()[0]
    else:
        if is_file_message:
            cursor.execute('''
                INSERT INTO messages (sender_id, receiver_id, message, message_type, media_url, media_type, file_size, reply_to_message_id, sent_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
            ''', (session['user_id'], receiver_id, message or file_name, 'file', file_url, file_type, file_size, reply_to_message_id))
        else:
            cursor.execute('''
                INSERT INTO messages (sender_id, receiver_id, message, reply_to_message_id, sent_at)
                VALUES (?, ?, ?, ?, datetime('now'))
            ''', (session['user_id'], receiver_id, message, reply_to_message_id))
        message_id = cursor.lastrowid

    # Get replied-to message info if this is a reply
    replied_message = None
    if reply_to_message_id:
        if USE_POSTGRES:
            cursor.execute('''
                SELECT m.id, m.message, m.message_type, m.media_url, m.sender_id, u.username
                FROM messages m
                JOIN users u ON m.sender_id = u.id
                WHERE m.id = %s
            ''', (reply_to_message_id,))
        else:
            cursor.execute('''
                SELECT m.id, m.message, m.message_type, m.media_url, m.sender_id, u.username
                FROM messages m
                JOIN users u ON m.sender_id = u.id
                WHERE m.id = ?
            ''', (reply_to_message_id,))

        replied_row = cursor.fetchone()
        if replied_row:
            replied_message = {
                'id': replied_row[0],
                'message': replied_row[1],
                'message_type': replied_row[2],
                'media_url': replied_row[3],
                'sender_id': replied_row[4],
                'sender_username': replied_row[5]
            }

    conn.commit()
    conn.close()

    # Get sender info
    sender = get_user_by_id(session['user_id'])

    # Prepare message data
    message_data = {
        'id': message_id,
        'sender_id': session['user_id'],
        'sender_username': sender[1] if sender else 'Unknown',
        'sender_avatar_color': sender[4] if sender and len(sender) > 4 else '#ff6b9d',
        'receiver_id': receiver_id,
        'message': message or file_name if is_file_message else message,
        'sent_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'message_type': 'file' if is_file_message else 'text',
        'temp_id': temp_id,
        'read_status': 'sent',  # sent, delivered, read
        'reply_to_message_id': reply_to_message_id,
        'replied_message': replied_message
    }

    # Add file data if it's a file message
    if is_file_message:
        message_data.update({
            'file_url': file_url,
            'file_name': file_name,
            'file_size': file_size,
            'file_type': file_type
        })

    # Send confirmation to sender (message sent)
    emit('message_sent', message_data)
    print(f"✅ Message sent confirmation to sender {session['user_id']}")

    # Send to receiver in their room
    receiver_room = f"user_{receiver_id}"
    socketio.emit('new_message', message_data, room=receiver_room)
    print(f"📨 Message sent to receiver room: {receiver_room}")

    # For now, don't automatically mark as delivered - let the receiver confirm delivery
    # This ensures accurate delivery status
    print(f"📤 Message sent to receiver: {message_id} (delivery confirmation pending)")

@socketio.on('mark_messages_read')
def handle_mark_messages_read(data):
    """Mark messages as read when user opens chat"""
    if 'user_id' not in session:
        return

    sender_id = data.get('sender_id')
    if not sender_id:
        return

    try:
        sender_id = int(sender_id)
        print(f"📖 Marking messages as read: receiver={session['user_id']}, sender={sender_id}")

        # Update messages in database to mark as read
        conn = get_db_connection()
        cursor = conn.cursor()

        if USE_POSTGRES:
            cursor.execute('''
                UPDATE messages
                SET read_at = NOW()
                WHERE receiver_id = %s AND sender_id = %s AND read_at IS NULL
                RETURNING id
            ''', (session['user_id'], sender_id))
            read_message_ids = [row[0] for row in cursor.fetchall()]
        else:
            cursor.execute('''
                UPDATE messages
                SET read_at = datetime('now')
                WHERE receiver_id = ? AND sender_id = ? AND read_at IS NULL
            ''', (session['user_id'], sender_id))

            # Get the updated message IDs
            cursor.execute('''
                SELECT id FROM messages
                WHERE receiver_id = ? AND sender_id = ? AND read_at IS NOT NULL
            ''', (session['user_id'], sender_id))
            read_message_ids = [row[0] for row in cursor.fetchall()]

        conn.commit()
        conn.close()

        if read_message_ids:
            # Notify sender that messages were read
            sender_room = f"user_{sender_id}"
            socketio.emit('messages_read', {
                'message_ids': read_message_ids,
                'reader_id': session['user_id']
            }, room=sender_room)
            print(f"📖 Notified sender {sender_id} that {len(read_message_ids)} messages were read")

    except Exception as e:
        print(f"❌ Error marking messages as read: {e}")

@socketio.on('mark_specific_messages_read')
def handle_mark_specific_messages_read(data):
    """Mark specific messages as read when they become visible"""
    if 'user_id' not in session:
        return

    sender_id = data.get('sender_id')
    message_ids = data.get('message_ids', [])

    if not sender_id or not message_ids:
        return

    try:
        sender_id = int(sender_id)
        message_ids = [int(mid) for mid in message_ids]
        print(f"📖 Marking specific messages as read: receiver={session['user_id']}, sender={sender_id}, messages={message_ids}")

        # Update specific messages in database to mark as read
        conn = get_db_connection()
        cursor = conn.cursor()

        if USE_POSTGRES:
            cursor.execute('''
                UPDATE messages
                SET read_at = NOW()
                WHERE id = ANY(%s) AND receiver_id = %s AND sender_id = %s AND read_at IS NULL
                RETURNING id
            ''', (message_ids, session['user_id'], sender_id))
            read_message_ids = [row[0] for row in cursor.fetchall()]
        else:
            placeholders = ','.join(['?' for _ in message_ids])
            cursor.execute(f'''
                UPDATE messages
                SET read_at = datetime('now')
                WHERE id IN ({placeholders}) AND receiver_id = ? AND sender_id = ? AND read_at IS NULL
            ''', message_ids + [session['user_id'], sender_id])

            # Get the updated message IDs
            cursor.execute(f'''
                SELECT id FROM messages
                WHERE id IN ({placeholders}) AND receiver_id = ? AND sender_id = ? AND read_at IS NOT NULL
            ''', message_ids + [session['user_id'], sender_id])
            read_message_ids = [row[0] for row in cursor.fetchall()]

        conn.commit()
        conn.close()

        if read_message_ids:
            # Notify sender that specific messages were read
            sender_room = f"user_{sender_id}"
            socketio.emit('messages_read', {
                'message_ids': read_message_ids,
                'reader_id': session['user_id']
            }, room=sender_room)
            print(f"📖 Notified sender {sender_id} that {len(read_message_ids)} specific messages were read")

    except Exception as e:
        print(f"❌ Error marking specific messages as read: {e}")

# Call Management Socket Events
@socketio.on('initiate_call')
def handle_initiate_call(data):
    """Handle call initiation"""
    print(f"📞 Call initiation request received: {data}")
    print(f"📞 From user: {session.get('user_id', 'unknown')}")

    if 'user_id' not in session:
        print("❌ No user_id in session for call initiation")
        emit('call_error', {'message': 'Not authenticated'})
        return

    receiver_id = data.get('receiver_id')
    call_type = data.get('call_type', 'audio')  # 'audio' or 'video'

    print(f"📞 Call details - Receiver: {receiver_id}, Type: {call_type}")

    if not receiver_id or call_type not in ['audio', 'video']:
        print(f"❌ Invalid call parameters - Receiver: {receiver_id}, Type: {call_type}")
        emit('call_error', {'message': 'Invalid call parameters'})
        return

    try:
        receiver_id = int(receiver_id)

        # Check if receiver is a valid contact
        conn = get_db_connection()
        cursor = conn.cursor()

        if USE_POSTGRES:
            cursor.execute('''
                SELECT COUNT(*) FROM contacts c
                JOIN users u ON c.contact_user_id = u.id
                WHERE c.user_id = %s AND u.id = %s
            ''', (session['user_id'], receiver_id))
        else:
            cursor.execute('''
                SELECT COUNT(*) FROM contacts c
                JOIN users u ON c.contact_user_id = u.id
                WHERE c.user_id = ? AND u.id = ?
            ''', (session['user_id'], receiver_id))

        if cursor.fetchone()[0] == 0:
            conn.close()
            emit('call_error', {'message': 'Invalid receiver'})
            return

        # Create call record
        if USE_POSTGRES:
            cursor.execute('''
                INSERT INTO calls (caller_id, receiver_id, call_type, call_status)
                VALUES (%s, %s, %s, 'initiated')
                RETURNING id
            ''', (session['user_id'], receiver_id, call_type))
            call_id = cursor.fetchone()[0]
        else:
            cursor.execute('''
                INSERT INTO calls (caller_id, receiver_id, call_type, call_status)
                VALUES (?, ?, ?, 'initiated')
            ''', (session['user_id'], receiver_id, call_type))
            call_id = cursor.lastrowid

        conn.commit()
        conn.close()

        # Get caller info
        caller = get_user_by_id(session['user_id'])

        # Prepare call data
        call_data = {
            'call_id': call_id,
            'caller_id': session['user_id'],
            'caller_username': caller[1] if caller else 'Unknown',
            'caller_avatar_color': caller[4] if caller and len(caller) > 4 else '#ff6b9d',
            'caller_profile_picture': caller[6] if caller and len(caller) > 6 else None,
            'call_type': call_type,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        # Send call invitation to receiver
        receiver_room = f"user_{receiver_id}"
        print(f"📞 Sending incoming_call to room: {receiver_room}")
        print(f"📞 Call data being sent: {call_data}")

        socketio.emit('incoming_call', call_data, room=receiver_room)
        print(f"📞 incoming_call event emitted to room {receiver_room}")

        # Confirm call initiation to caller
        caller_confirmation = {
            'call_id': call_id,
            'receiver_id': receiver_id,
            'call_type': call_type,
            'status': 'calling'
        }
        print(f"📞 Sending call_initiated confirmation to caller: {caller_confirmation}")

        emit('call_initiated', caller_confirmation)

        print(f"📞 Call initiated: {session['user_id']} -> {receiver_id} ({call_type})")

        # Set up a timeout to mark call as missed if not answered within 30 seconds
        def mark_call_missed():
            try:
                conn = get_db_connection()
                cursor = conn.cursor()

                # Check if call is still in 'initiated' status
                cursor.execute('''
                    SELECT call_status FROM calls WHERE id = {}
                '''.format('%s' if USE_POSTGRES else '?'), (call_id,))
                result = cursor.fetchone()

                if result and result[0] == 'initiated':
                    # Mark as missed
                    if USE_POSTGRES:
                        cursor.execute('''
                            UPDATE calls SET call_status = 'missed', ended_at = NOW()
                            WHERE id = %s
                        ''', (call_id,))
                    else:
                        cursor.execute('''
                            UPDATE calls SET call_status = 'missed', ended_at = datetime('now')
                            WHERE id = ?
                        ''', (call_id,))

                    conn.commit()
                    conn.close()

                    # Create missed call history message
                    create_call_history_message(session['user_id'], receiver_id, call_type, 'missed')

                    print(f"📞 Call {call_id} marked as missed due to timeout")
                else:
                    conn.close()

            except Exception as e:
                print(f"❌ Error marking call as missed: {e}")

        # Schedule the timeout (30 seconds)
        import threading
        timer = threading.Timer(30.0, mark_call_missed)
        timer.start()

    except Exception as e:
        print(f"❌ Error initiating call: {e}")
        emit('call_error', {'message': 'Failed to initiate call'})

@socketio.on('answer_call')
def handle_answer_call(data):
    """Handle call answer"""
    if 'user_id' not in session:
        return

    call_id = data.get('call_id')
    if not call_id:
        return

    try:
        call_id = int(call_id)

        # Update call status to accepted
        conn = get_db_connection()
        cursor = conn.cursor()

        if USE_POSTGRES:
            cursor.execute('''
                UPDATE calls
                SET call_status = 'accepted', answered_at = NOW()
                WHERE id = %s AND receiver_id = %s AND call_status = 'initiated'
                RETURNING caller_id, call_type
            ''', (call_id, session['user_id']))
            result = cursor.fetchone()
        else:
            cursor.execute('''
                UPDATE calls
                SET call_status = 'accepted', answered_at = datetime('now')
                WHERE id = ? AND receiver_id = ? AND call_status = 'initiated'
            ''', (call_id, session['user_id']))

            cursor.execute('''
                SELECT caller_id, call_type FROM calls
                WHERE id = ? AND receiver_id = ?
            ''', (call_id, session['user_id']))
            result = cursor.fetchone()

        if not result:
            conn.close()
            emit('call_error', {'message': 'Invalid call'})
            return

        caller_id, call_type = result
        conn.commit()
        conn.close()

        # Get receiver info
        receiver = get_user_by_id(session['user_id'])

        # Notify caller that call was accepted
        caller_room = f"user_{caller_id}"
        socketio.emit('call_accepted', {
            'call_id': call_id,
            'receiver_id': session['user_id'],
            'receiver_username': receiver[1] if receiver else 'Unknown',
            'receiver_avatar_color': receiver[4] if receiver and len(receiver) > 4 else '#ff6b9d',
            'receiver_profile_picture': receiver[6] if receiver and len(receiver) > 6 else None,
            'call_type': call_type
        }, room=caller_room)

        # Confirm to receiver
        emit('call_answered', {
            'call_id': call_id,
            'call_type': call_type,
            'status': 'connected'
        })

        print(f"✅ Call answered: {call_id}")

    except Exception as e:
        print(f"❌ Error answering call: {e}")
        emit('call_error', {'message': 'Failed to answer call'})

@socketio.on('reject_call')
def handle_reject_call(data):
    """Handle call rejection"""
    if 'user_id' not in session:
        return

    call_id = data.get('call_id')
    if not call_id:
        return

    try:
        call_id = int(call_id)

        # Update call status to rejected
        conn = get_db_connection()
        cursor = conn.cursor()

        if USE_POSTGRES:
            cursor.execute('''
                UPDATE calls
                SET call_status = 'rejected', ended_at = NOW()
                WHERE id = %s AND receiver_id = %s AND call_status = 'initiated'
                RETURNING caller_id, call_type
            ''', (call_id, session['user_id']))
            result = cursor.fetchone()
        else:
            cursor.execute('''
                UPDATE calls
                SET call_status = 'rejected', ended_at = datetime('now')
                WHERE id = ? AND receiver_id = ? AND call_status = 'initiated'
            ''', (call_id, session['user_id']))

            cursor.execute('''
                SELECT caller_id, call_type FROM calls
                WHERE id = ? AND receiver_id = ?
            ''', (call_id, session['user_id']))
            result = cursor.fetchone()

        if result:
            caller_id = result[0]
            call_type = result[1] if len(result) > 1 else 'audio'
            conn.commit()

            # Create call history message for rejected call
            create_call_history_message(caller_id, session['user_id'], call_type, 'rejected')

            # Notify caller that call was rejected
            caller_room = f"user_{caller_id}"
            socketio.emit('call_rejected', {
                'call_id': call_id,
                'message': 'Call was declined'
            }, room=caller_room)

            print(f"❌ Call rejected: {call_id}")

        conn.close()

    except Exception as e:
        print(f"❌ Error rejecting call: {e}")

def create_call_history_message(caller_id, receiver_id, call_type, call_status, duration=None):
    """Create a call history message in the chat - WhatsApp style (single message)"""
    try:
        call_icon = "📞" if call_type == "audio" else "📹"

        # Create ONE message from caller to receiver (like WhatsApp)
        # The message text will be interpreted differently by each user when displayed
        if call_status == 'missed':
            message_text = f"{call_icon} Missed call"
        elif call_status == 'rejected':
            message_text = f"{call_icon} Call declined"
        elif call_status == 'ended' and duration:
            duration_str = format_call_duration(duration)
            message_text = f"{call_icon} Call • {duration_str}"
        else:
            message_text = f"{call_icon} Call"

        # Insert ONE call message into database (from caller to receiver)
        conn = get_db_connection()
        cursor = conn.cursor()

        if USE_POSTGRES:
            cursor.execute('''
                INSERT INTO messages (sender_id, receiver_id, message, message_type, call_status, call_duration, sent_at)
                VALUES (%s, %s, %s, %s, %s, %s, NOW())
                RETURNING id
            ''', (caller_id, receiver_id, message_text, 'call', call_status, duration))
            message_id = cursor.fetchone()[0]
        else:
            cursor.execute('''
                INSERT INTO messages (sender_id, receiver_id, message, message_type, call_status, call_duration, sent_at)
                VALUES (?, ?, ?, ?, ?, ?, datetime('now'))
            ''', (caller_id, receiver_id, message_text, 'call', call_status, duration))
            message_id = cursor.lastrowid

        conn.commit()
        conn.close()

        # Get caller info for the message
        caller = get_user_by_id(caller_id)

        # Prepare message data for real-time update
        message_data = {
            'id': message_id,
            'sender_id': caller_id,
            'sender_username': caller[1] if caller else 'Unknown',
            'sender_avatar_color': caller[4] if caller and len(caller) > 4 else '#ff6b9d',
            'receiver_id': receiver_id,
            'message': message_text,
            'message_type': 'call',
            'call_status': call_status,
            'call_duration': duration,
            'sent_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        # Send to both users in their respective rooms
        caller_room = f"user_{caller_id}"
        receiver_room = f"user_{receiver_id}"

        # Send to caller (mark as own message)
        socketio.emit('new_message', {**message_data, 'is_own': True}, room=caller_room)

        # Send to receiver (mark as not own)
        socketio.emit('new_message', {**message_data, 'is_own': False}, room=receiver_room)

        print(f"📞 Created call history message: {caller_id} -> {receiver_id} ({call_status})")

    except Exception as e:
        print(f"❌ Error creating call history message: {e}")

def format_call_duration(duration_seconds):
    """Format call duration in a human-readable format"""
    if not duration_seconds:
        return "0:00"

    duration_seconds = int(duration_seconds)
    hours = duration_seconds // 3600
    minutes = (duration_seconds % 3600) // 60
    seconds = duration_seconds % 60

    if hours > 0:
        return f"{hours}:{minutes:02d}:{seconds:02d}"
    else:
        return f"{minutes}:{seconds:02d}"

@socketio.on('end_call')
def handle_end_call(data):
    """Handle call termination"""
    if 'user_id' not in session:
        return

    call_id = data.get('call_id')
    if not call_id:
        return

    try:
        call_id = int(call_id)

        # Update call status to ended and calculate duration
        conn = get_db_connection()
        cursor = conn.cursor()

        # First get call details before updating
        cursor.execute('''
            SELECT caller_id, receiver_id, call_type, call_status, answered_at, started_at
            FROM calls WHERE id = {}
        '''.format('%s' if USE_POSTGRES else '?'), (call_id,))
        call_details = cursor.fetchone()

        if not call_details:
            conn.close()
            return

        caller_id, receiver_id, call_type, current_status, answered_at, started_at = call_details

        if USE_POSTGRES:
            cursor.execute('''
                UPDATE calls
                SET call_status = 'ended', ended_at = NOW(),
                    duration = EXTRACT(EPOCH FROM (NOW() - COALESCE(answered_at, started_at)))
                WHERE id = %s AND (caller_id = %s OR receiver_id = %s) AND call_status IN ('accepted', 'initiated')
                RETURNING duration
            ''', (call_id, session['user_id'], session['user_id']))
            duration_result = cursor.fetchone()
            duration = duration_result[0] if duration_result else 0
        else:
            cursor.execute('''
                UPDATE calls
                SET call_status = 'ended', ended_at = datetime('now'),
                    duration = (julianday(datetime('now')) - julianday(COALESCE(answered_at, started_at))) * 86400
                WHERE id = ? AND (caller_id = ? OR receiver_id = ?) AND call_status IN ('accepted', 'initiated')
            ''', (call_id, session['user_id'], session['user_id']))

            # Get the updated duration
            cursor.execute('''
                SELECT duration FROM calls WHERE id = ?
            ''', (call_id,))
            duration_result = cursor.fetchone()
            duration = duration_result[0] if duration_result else 0

        conn.commit()
        conn.close()

        # Create call history message with duration
        if current_status == 'accepted':
            # Call was answered, create "ended" message with duration
            create_call_history_message(caller_id, receiver_id, call_type, 'ended', duration)
        else:
            # Call was not answered, create "missed" message
            create_call_history_message(caller_id, receiver_id, call_type, 'missed')

        # Notify the other participant that call ended
        other_user_id = receiver_id if session['user_id'] == caller_id else caller_id
        other_user_room = f"user_{other_user_id}"
        socketio.emit('call_ended', {
            'call_id': call_id,
            'ended_by': session['user_id']
        }, room=other_user_room)

        print(f"📞 Call ended: {call_id} (duration: {duration}s)")

    except Exception as e:
        print(f"❌ Error ending call: {e}")

# WebRTC Signaling Events
@socketio.on('webrtc_offer')
def handle_webrtc_offer(data):
    """Handle WebRTC offer for call establishment"""
    if 'user_id' not in session:
        return

    call_id = data.get('call_id')
    receiver_id = data.get('receiver_id')
    offer = data.get('offer')

    if not all([call_id, receiver_id, offer]):
        return

    # Forward offer to receiver
    receiver_room = f"user_{receiver_id}"
    socketio.emit('webrtc_offer', {
        'call_id': call_id,
        'caller_id': session['user_id'],
        'offer': offer
    }, room=receiver_room)

    print(f"📡 WebRTC offer forwarded: {call_id}")

@socketio.on('webrtc_answer')
def handle_webrtc_answer(data):
    """Handle WebRTC answer for call establishment"""
    if 'user_id' not in session:
        return

    call_id = data.get('call_id')
    caller_id = data.get('caller_id')
    answer = data.get('answer')

    if not all([call_id, caller_id, answer]):
        return

    # Forward answer to caller
    caller_room = f"user_{caller_id}"
    socketio.emit('webrtc_answer', {
        'call_id': call_id,
        'receiver_id': session['user_id'],
        'answer': answer
    }, room=caller_room)

    print(f"📡 WebRTC answer forwarded: {call_id}")

@socketio.on('webrtc_ice_candidate')
def handle_webrtc_ice_candidate(data):
    """Handle WebRTC ICE candidate exchange"""
    if 'user_id' not in session:
        return

    call_id = data.get('call_id')
    target_user_id = data.get('target_user_id')
    candidate = data.get('candidate')

    if not all([call_id, target_user_id, candidate]):
        return

    # Forward ICE candidate to target user
    target_room = f"user_{target_user_id}"
    socketio.emit('webrtc_ice_candidate', {
        'call_id': call_id,
        'from_user_id': session['user_id'],
        'candidate': candidate
    }, room=target_room)

    print(f"🧊 ICE candidate forwarded: {call_id}")

@app.route('/api/messages/<int:contact_id>')
def get_messages(contact_id):
    """Get messages between current user and contact"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401

    conn = get_db_connection()
    cursor = conn.cursor()

    # Try to get messages with basic columns first
    try:
        if USE_POSTGRES:
            cursor.execute('''
                SELECT m.id, m.sender_id, m.receiver_id, m.message, m.sent_at,
                       u.username, u.avatar_color, m.read_at, m.message_type, m.media_url,
                       m.reply_to_message_id, m.media_type, m.file_size,
                       rm.message as replied_message, rm.message_type as replied_message_type,
                       rm.media_url as replied_media_url, ru.username as replied_sender_username,
                       m.call_status, m.call_duration
                FROM messages m
                JOIN users u ON m.sender_id = u.id
                LEFT JOIN messages rm ON m.reply_to_message_id = rm.id
                LEFT JOIN users ru ON rm.sender_id = ru.id
                WHERE (m.sender_id = %s AND m.receiver_id = %s)
                   OR (m.sender_id = %s AND m.receiver_id = %s)
                ORDER BY m.sent_at ASC
            ''', (session['user_id'], contact_id, contact_id, session['user_id']))
        else:
            cursor.execute('''
                SELECT m.id, m.sender_id, m.receiver_id, m.message, m.sent_at,
                       u.username, u.avatar_color, m.read_at, m.message_type, m.media_url,
                       m.reply_to_message_id, m.media_type, m.file_size,
                       rm.message as replied_message, rm.message_type as replied_message_type,
                       rm.media_url as replied_media_url, ru.username as replied_sender_username,
                       m.call_status, m.call_duration
                FROM messages m
                JOIN users u ON m.sender_id = u.id
                LEFT JOIN messages rm ON m.reply_to_message_id = rm.id
                LEFT JOIN users ru ON rm.sender_id = ru.id
                WHERE (m.sender_id = ? AND m.receiver_id = ?)
                   OR (m.sender_id = ? AND m.receiver_id = ?)
                ORDER BY m.sent_at ASC
            ''', (session['user_id'], contact_id, contact_id, session['user_id']))
    except Exception as e:
        print(f"❌ Error querying messages: {e}")
        # Fallback to basic query without sent_at/read_at if columns don't exist
        try:
            cursor.execute('''
                SELECT m.id, m.sender_id, m.receiver_id, m.message,
                       u.username, u.avatar_color
                FROM messages m
                JOIN users u ON m.sender_id = u.id
                WHERE (m.sender_id = ? AND m.receiver_id = ?)
                   OR (m.sender_id = ? AND m.receiver_id = ?)
                ORDER BY m.id ASC
            ''', (session['user_id'], contact_id, contact_id, session['user_id']))
        except Exception as e2:
            print(f"❌ Fallback query also failed: {e2}")
            conn.close()
            return jsonify({'messages': [], 'error': 'Database error'})

    messages = []
    for row in cursor.fetchall():
        # Debug: Print raw database row
        print(f"🔍 Raw DB row: ID={row[0]}, columns={len(row)}, read_at={row[7] if len(row) > 7 else 'N/A'}")

        # Handle different column counts - be more flexible
        try:
            # Basic message data (always present)
            message_data = {
                'id': row[0],
                'sender_id': row[1],
                'receiver_id': row[2],
                'message': row[3],
                'sent_at': row[4] if len(row) > 4 else None,
                'sender_username': row[5] if len(row) > 5 else 'Unknown',
                'sender_avatar_color': row[6] if len(row) > 6 else '#ff6b9d',
                'read_at': row[7] if len(row) > 7 else None,
                'message_type': row[8] if len(row) > 8 else 'text',
                'media_url': row[9] if len(row) > 9 else None,
                'reply_to_message_id': row[10] if len(row) > 10 else None,
                'is_own': row[1] == session['user_id']
            }

            # Add call data if available (columns 17, 18)
            if len(row) >= 19:
                message_data['call_status'] = row[17]
                message_data['call_duration'] = row[18]

            # Add reply data if available
            replied_message = None
            if len(row) >= 17 and row[10]:  # reply_to_message_id exists and we have reply columns
                replied_message = {
                    'id': row[10],
                    'message': row[13] if len(row) > 13 else '',
                    'message_type': row[14] if len(row) > 14 else 'text',
                    'media_url': row[15] if len(row) > 15 else None,
                    'sender_username': row[16] if len(row) > 16 else 'Unknown'
                }
                message_data['replied_message'] = replied_message

            # Add file-specific data if it's a file message
            if message_data.get('message_type') == 'file' and len(row) >= 13:
                message_data.update({
                    'file_url': row[9],  # media_url is file_url for files
                    'file_type': row[11] if len(row) > 11 else None,  # media_type
                    'media_type': row[11] if len(row) > 11 else None,  # media_type
                    'file_size': row[12] if len(row) > 12 else None,  # file_size
                    'file_name': row[3]   # message contains file name for files
                })

            messages.append(message_data)

        except Exception as e:
            print(f"❌ Error processing message row {row[0] if len(row) > 0 else 'unknown'}: {e}")
            continue

    conn.close()

    print(f"📚 Loaded {len(messages)} messages for contact {contact_id}")
    return jsonify({'messages': messages})

    # Get reactions for all messages
    if messages:
        message_ids = [str(msg['id']) for msg in messages]
        if USE_POSTGRES:
            cursor.execute('''
                SELECT mr.message_id, mr.emoji, COUNT(*) as count,
                       ARRAY_AGG(u.username) as usernames
                FROM message_reactions mr
                JOIN users u ON mr.user_id = u.id
                WHERE mr.message_id = ANY(%s)
                GROUP BY mr.message_id, mr.emoji
            ''', (message_ids,))
        else:
            placeholders = ','.join(['?' for _ in message_ids])
            cursor.execute(f'''
                SELECT mr.message_id, mr.emoji, COUNT(*) as count,
                       GROUP_CONCAT(u.username) as usernames
                FROM message_reactions mr
                JOIN users u ON mr.user_id = u.id
                WHERE mr.message_id IN ({placeholders})
                GROUP BY mr.message_id, mr.emoji
            ''', message_ids)

        # Organize reactions by message_id
        reactions_by_message = {}
        for row in cursor.fetchall():
            message_id = row[0]
            if message_id not in reactions_by_message:
                reactions_by_message[message_id] = []
            reactions_by_message[message_id].append({
                'emoji': row[1],
                'count': row[2],
                'usernames': row[3].split(',') if USE_POSTGRES else row[3].split(',') if row[3] else []
            })

        # Add reactions to messages
        for message in messages:
            message['reactions'] = reactions_by_message.get(message['id'], [])

    conn.close()
    print(f"📚 Loaded {len(messages)} messages for contact {contact_id}")
    return jsonify({'messages': messages})

@app.route('/upload_image', methods=['POST'])
def upload_image():
    """Upload image to Cloudinary and send as message"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': 'Not authenticated'}), 401

    if 'image' not in request.files:
        return jsonify({'success': False, 'message': 'No image file provided'}), 400

    file = request.files['image']
    receiver_id = request.form.get('receiver_id')

    if not receiver_id:
        return jsonify({'success': False, 'message': 'No receiver specified'}), 400

    if file.filename == '':
        return jsonify({'success': False, 'message': 'No file selected'}), 400

    # Check file type
    allowed_extensions = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
    file_extension = file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else ''

    if file_extension not in allowed_extensions:
        return jsonify({'success': False, 'message': 'Invalid file type. Only images allowed.'}), 400

    try:
        import cloudinary
        import cloudinary.uploader

        # Configure Cloudinary (using user's credentials from memory)
        cloudinary.config(
            cloud_name="dhintkx9g",
            api_key="141354373965837",
            api_secret="wetCyuAA-PphcvjfbmjwRxrrpGs"
        )

        # Upload to Cloudinary
        upload_result = cloudinary.uploader.upload(
            file,
            folder="chat_images",
            resource_type="image",
            transformation=[
                {'width': 800, 'height': 600, 'crop': 'limit'},
                {'quality': 'auto:good'}
            ]
        )

        # Save message to database
        conn = get_db_connection()
        cursor = conn.cursor()

        if USE_POSTGRES:
            cursor.execute('''
                INSERT INTO messages (sender_id, receiver_id, message, message_type, media_url, media_type, media_public_id, file_size)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING id
            ''', (
                session['user_id'],
                receiver_id,
                'Photo',
                'image',
                upload_result['secure_url'],
                'image',
                upload_result['public_id'],
                upload_result.get('bytes', 0)
            ))
            message_id = cursor.fetchone()[0]
        else:
            cursor.execute('''
                INSERT INTO messages (sender_id, receiver_id, message, message_type, media_url, media_type, media_public_id, file_size)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                session['user_id'],
                receiver_id,
                'Photo',
                'image',
                upload_result['secure_url'],
                'image',
                upload_result['public_id'],
                upload_result.get('bytes', 0)
            ))
            message_id = cursor.lastrowid

        conn.commit()

        # Get sender info
        cursor.execute('SELECT username, avatar_color FROM users WHERE id = ?', (session['user_id'],))
        sender = cursor.fetchone()
        conn.close()

        # Prepare message data
        message_data = {
            'id': message_id,
            'sender_id': session['user_id'],
            'receiver_id': int(receiver_id),
            'message': 'Photo',
            'message_type': 'image',
            'media_url': upload_result['secure_url'],
            'media_type': 'image',
            'sent_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'sender_username': sender[0] if sender else 'Unknown',
            'sender_avatar_color': sender[1] if sender and len(sender) > 1 else '#ff6b9d',
            'is_own': True
        }

        # Send via Socket.IO to receiver
        receiver_room = f"user_{receiver_id}"
        socketio.emit('new_message', {**message_data, 'is_own': False}, room=receiver_room)

        print(f"📷 Image message sent: {message_id}")
        return jsonify({'success': True, 'message': message_data})

    except Exception as e:
        print(f"❌ Error uploading image: {e}")
        return jsonify({'success': False, 'message': f'Upload failed: {str(e)}'}), 500

@app.route('/add_reaction', methods=['POST'])
def add_reaction():
    """Add emoji reaction to a message"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': 'Not authenticated'}), 401

    try:
        data = request.get_json()
        message_id = data.get('message_id')
        emoji = data.get('emoji')

        if not message_id or not emoji:
            return jsonify({'success': False, 'message': 'Missing message_id or emoji'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        # Check if user already reacted to this message
        if USE_POSTGRES:
            cursor.execute('SELECT id FROM message_reactions WHERE message_id = %s AND user_id = %s',
                         (message_id, session['user_id']))
        else:
            cursor.execute('SELECT id FROM message_reactions WHERE message_id = ? AND user_id = ?',
                         (message_id, session['user_id']))

        existing_reaction = cursor.fetchone()

        if existing_reaction:
            # Update existing reaction
            if USE_POSTGRES:
                cursor.execute('UPDATE message_reactions SET emoji = %s WHERE message_id = %s AND user_id = %s',
                             (emoji, message_id, session['user_id']))
            else:
                cursor.execute('UPDATE message_reactions SET emoji = ? WHERE message_id = ? AND user_id = ?',
                             (emoji, message_id, session['user_id']))
        else:
            # Add new reaction
            if USE_POSTGRES:
                cursor.execute('INSERT INTO message_reactions (message_id, user_id, emoji) VALUES (%s, %s, %s)',
                             (message_id, session['user_id'], emoji))
            else:
                cursor.execute('INSERT INTO message_reactions (message_id, user_id, emoji) VALUES (?, ?, ?)',
                             (message_id, session['user_id'], emoji))

        conn.commit()

        # Get updated reaction counts
        if USE_POSTGRES:
            cursor.execute('''
                SELECT emoji, COUNT(*) as count,
                       ARRAY_AGG(u.username) as usernames
                FROM message_reactions mr
                JOIN users u ON mr.user_id = u.id
                WHERE mr.message_id = %s
                GROUP BY emoji
            ''', (message_id,))
        else:
            cursor.execute('''
                SELECT emoji, COUNT(*) as count,
                       GROUP_CONCAT(u.username) as usernames
                FROM message_reactions mr
                JOIN users u ON mr.user_id = u.id
                WHERE mr.message_id = ?
                GROUP BY emoji
            ''', (message_id,))

        reactions = []
        for row in cursor.fetchall():
            reactions.append({
                'emoji': row[0],
                'count': row[1],
                'usernames': row[2].split(',') if USE_POSTGRES else row[2].split(',') if row[2] else []
            })

        conn.close()

        # Emit reaction update to all users in the chat
        socketio.emit('reaction_updated', {
            'message_id': message_id,
            'reactions': reactions
        })

        print(f"👍 Reaction added: {emoji} to message {message_id}")
        return jsonify({'success': True, 'reactions': reactions})

    except Exception as e:
        print(f"❌ Error adding reaction: {e}")
        return jsonify({'success': False, 'message': f'Failed to add reaction: {str(e)}'}), 500

@app.route('/get_reactions/<int:message_id>')
def get_reactions(message_id):
    """Get reactions for a specific message"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        if USE_POSTGRES:
            cursor.execute('''
                SELECT emoji, COUNT(*) as count,
                       ARRAY_AGG(u.username) as usernames
                FROM message_reactions mr
                JOIN users u ON mr.user_id = u.id
                WHERE mr.message_id = %s
                GROUP BY emoji
            ''', (message_id,))
        else:
            cursor.execute('''
                SELECT emoji, COUNT(*) as count,
                       GROUP_CONCAT(u.username) as usernames
                FROM message_reactions mr
                JOIN users u ON mr.user_id = u.id
                WHERE mr.message_id = ?
                GROUP BY emoji
            ''', (message_id,))

        reactions = []
        for row in cursor.fetchall():
            reactions.append({
                'emoji': row[0],
                'count': row[1],
                'usernames': row[2].split(',') if USE_POSTGRES else row[2].split(',') if row[2] else []
            })

        conn.close()
        return jsonify({'reactions': reactions})

    except Exception as e:
        print(f"❌ Error getting reactions: {e}")
        return jsonify({'reactions': []})





# Initialize database on startup
init_db()
migrate_db()

if __name__ == '__main__':
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
